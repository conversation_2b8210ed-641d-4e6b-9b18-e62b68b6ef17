"""
测试违例数量估算功能的准确性
"""

import os
import tempfile
import unittest
from performance_optimizer import PerformanceOptimizer


class TestViolationEstimation(unittest.TestCase):
    """违例数量估算测试"""
    
    def setUp(self):
        """测试初始化"""
        self.optimizer = PerformanceOptimizer()
        self.test_files = []
    
    def tearDown(self):
        """清理测试文件"""
        for file_path in self.test_files:
            if os.path.exists(file_path):
                os.remove(file_path)
    
    def create_test_file(self, violation_count: int, lines_per_violation: int = 5) -> str:
        """创建测试文件
        
        Args:
            violation_count: 违例数量
            lines_per_violation: 每个违例的行数
            
        Returns:
            str: 测试文件路径
        """
        fd, file_path = tempfile.mkstemp(suffix='.log', text=True)
        self.test_files.append(file_path)
        
        with os.fdopen(fd, 'w') as f:
            for i in range(violation_count):
                # 写入违例相关行
                f.write(f"Timing violation #{i+1}\n")
                f.write(f"Setup time: 1.5ns\n")
                f.write(f"Hold time: 0.8ns\n")
                f.write(f"Required: 2.0ns\n")
                f.write(f"Slack: -0.5ns\n")
                
                # 如果需要更多行，添加额外行
                for j in range(lines_per_violation - 5):
                    f.write(f"Additional line {j+1}\n")
        
        return file_path
    
    def test_basic_estimation_small_file(self):
        """测试小文件的基础估算"""
        # 创建包含100个违例的测试文件
        file_path = self.create_test_file(100, 5)
        actual_count = 100
        
        # 测试基础估算
        file_size = os.path.getsize(file_path)
        estimated = self.optimizer._estimate_record_count(file_size)
        
        # 验证估算结果在合理范围内（±30%）
        error_rate = abs(estimated - actual_count) / actual_count
        self.assertLess(error_rate, 0.3, f"基础估算误差过大: {error_rate:.2%}")
    
    def test_sampling_estimation_accuracy(self):
        """测试采样估算的准确性"""
        # 创建包含500个违例的测试文件
        file_path = self.create_test_file(500, 5)
        actual_count = 500
        
        # 测试采样估算
        estimated = self.optimizer.estimate_violation_count_with_sampling(file_path, 200)
        
        # 验证采样估算结果
        error_rate = abs(estimated - actual_count) / actual_count
        self.assertLess(error_rate, 0.2, f"采样估算误差过大: {error_rate:.2%}")
    
    def test_estimation_validation(self):
        """测试估算验证功能"""
        # 创建包含200个违例的测试文件
        file_path = self.create_test_file(200, 5)
        actual_count = 200
        
        # 验证估算准确性
        validation_result = self.optimizer.validate_estimation_accuracy(file_path, actual_count)
        
        # 检查验证结果
        self.assertIn('actual_count', validation_result)
        self.assertIn('basic_estimate', validation_result)
        self.assertIn('sampling_estimate', validation_result)
        self.assertIn('basic_accuracy', validation_result)
        self.assertIn('sampling_accuracy', validation_result)
        
        # 验证准确性指标
        self.assertGreaterEqual(validation_result['basic_accuracy'], 0)
        self.assertLessEqual(validation_result['basic_accuracy'], 1)
        self.assertGreaterEqual(validation_result['sampling_accuracy'], 0)
        self.assertLessEqual(validation_result['sampling_accuracy'], 1)
    
    def test_different_violation_counts(self):
        """测试不同违例数量的估算准确性"""
        test_counts = [50, 500, 2000, 5000]
        
        for count in test_counts:
            with self.subTest(violation_count=count):
                file_path = self.create_test_file(count, 5)
                
                # 测试采样估算
                estimated = self.optimizer.estimate_violation_count_with_sampling(file_path, 100)
                error_rate = abs(estimated - count) / count
                
                # 根据文件大小调整容错率
                tolerance = 0.3 if count < 1000 else 0.2
                self.assertLess(error_rate, tolerance, 
                              f"违例数量 {count} 的估算误差过大: {error_rate:.2%}")
    
    def test_empty_file_handling(self):
        """测试空文件处理"""
        fd, file_path = tempfile.mkstemp(suffix='.log', text=True)
        self.test_files.append(file_path)
        os.close(fd)  # 创建空文件
        
        # 测试空文件估算
        estimated = self.optimizer.estimate_violation_count_with_sampling(file_path)
        self.assertEqual(estimated, 0, "空文件应该返回0个违例")
    
    def test_file_with_no_violations(self):
        """测试不包含违例的文件"""
        fd, file_path = tempfile.mkstemp(suffix='.log', text=True)
        self.test_files.append(file_path)
        
        with os.fdopen(fd, 'w') as f:
            # 写入不包含违例关键词的内容
            for i in range(100):
                f.write(f"Normal log line {i+1}\n")
                f.write(f"Information message\n")
                f.write(f"Debug output\n")
        
        # 测试估算
        estimated = self.optimizer.estimate_violation_count_with_sampling(file_path)
        
        # 应该基于5行每违例的公式返回一个较小的数值
        self.assertGreater(estimated, 0, "即使没有违例关键词，也应该基于行数返回估算值")
        self.assertLess(estimated, 100, "估算值应该合理")


if __name__ == '__main__':
    unittest.main()