#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试corner无关的历史确认功能和导出功能
"""

import os
import sys
import sqlite3
import tempfile
from datetime import datetime

# 添加插件路径到sys.path
plugin_dir = os.path.dirname(os.path.abspath(__file__))
if plugin_dir not in sys.path:
    sys.path.insert(0, plugin_dir)

from models import ViolationDataModel

def test_corner_independent_confirmations():
    """测试corner无关的历史确认功能"""
    print("=== 测试corner无关的历史确认功能 ===")
    
    # 创建临时数据库
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_file:
        temp_db_path = tmp_file.name
    
    try:
        # 创建数据模型实例
        model = ViolationDataModel()
        model.db_path = temp_db_path
        model.init_database()
        
        # 1. 添加一些测试违例数据（不同corner）
        test_violations = [
            {
                'NUM': 1,
                'Hier': 'tb_top.cpu.core0',
                'Time': '1000 FS',
                'Check': 'setup(posedge clk, data)',
                'time_fs': 1000
            },
            {
                'NUM': 2,
                'Hier': 'tb_top.cpu.core1',
                'Time': '2000 FS',
                'Check': 'hold(posedge clk, data)',
                'time_fs': 2000
            }
        ]
        
        # 添加到不同corner
        corners = ['npg_f1_ssg', 'npg_f2_ffg', 'default']
        case_name = 'test_case'
        
        for corner in corners:
            model.add_violations(test_violations, case_name, corner, f'/path/to/{corner}/log')
            print(f"添加违例到corner: {corner}")
        
        # 2. 手动确认一个违例并保存为历史模式
        violations = model.get_violations_by_case(case_name, 'npg_f1_ssg')
        if violations:
            violation = violations[0]
            violation_id = violation['id']
            
            # 更新确认状态
            model.update_confirmation(
                violation_id, 'confirmed', 'test_user', 'pass', 
                'This is a test confirmation', False
            )
            
            # 保存为历史模式
            model.save_pattern(
                violation['hier'], violation['check_info'], 
                'test_user', 'pass', 'This is a test confirmation'
            )
            print(f"保存历史模式: {violation['hier']} - {violation['check_info']}")
        
        # 3. 测试corner无关的历史确认应用
        print("\n--- 测试corner无关历史确认应用 ---")
        
        # 应用到所有corner（corner无关模式）
        applied_count = model.apply_historical_confirmations(case_name, None)
        print(f"corner无关模式应用历史确认: {applied_count} 条")
        
        # 验证结果
        for corner in corners:
            violations = model.get_violations_by_case(case_name, corner)
            confirmed_count = sum(1 for v in violations if v.get('status') == 'confirmed')
            print(f"Corner {corner}: 确认数量 = {confirmed_count}")
        
        # 4. 测试导出功能
        print("\n--- 测试导出功能 ---")
        
        # 测试导出Excel
        excel_path = os.path.join(tempfile.gettempdir(), 'test_export.xlsx')
        try:
            success = model.export_patterns_to_excel(excel_path)
            if success:
                print(f"Excel导出成功: {excel_path}")
                if os.path.exists(excel_path):
                    print(f"Excel文件大小: {os.path.getsize(excel_path)} bytes")
                    os.remove(excel_path)  # 清理
            else:
                print("Excel导出失败")
        except ImportError as e:
            print(f"Excel导出跳过（缺少依赖）: {e}")
        
        # 测试导出CSV
        csv_path = os.path.join(tempfile.gettempdir(), 'test_export.csv')
        success = model.export_patterns_to_csv(csv_path)
        if success:
            print(f"CSV导出成功: {csv_path}")
            if os.path.exists(csv_path):
                with open(csv_path, 'r', encoding='utf-8-sig') as f:
                    content = f.read()
                    print(f"CSV内容预览:\n{content[:200]}...")
                os.remove(csv_path)  # 清理
        else:
            print("CSV导出失败")
        
        print("\n=== 测试完成 ===")
        
    finally:
        # 清理临时数据库
        if os.path.exists(temp_db_path):
            os.remove(temp_db_path)

def test_database_structure():
    """测试数据库结构"""
    print("\n=== 测试数据库结构 ===")
    
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_file:
        temp_db_path = tmp_file.name
    
    try:
        model = ViolationDataModel()
        model.db_path = temp_db_path
        model.init_database()
        
        # 检查表结构
        conn = sqlite3.connect(temp_db_path)
        cursor = conn.cursor()
        
        # 检查violation_patterns表
        cursor.execute("PRAGMA table_info(violation_patterns)")
        columns = cursor.fetchall()
        print("violation_patterns表结构:")
        for col in columns:
            print(f"  {col[1]} {col[2]}")
        
        # 检查timing_violations表
        cursor.execute("PRAGMA table_info(timing_violations)")
        columns = cursor.fetchall()
        print("\ntiming_violations表结构:")
        for col in columns:
            print(f"  {col[1]} {col[2]}")
        
        conn.close()
        
    finally:
        if os.path.exists(temp_db_path):
            os.remove(temp_db_path)

if __name__ == "__main__":
    test_database_structure()
    test_corner_independent_confirmations()
